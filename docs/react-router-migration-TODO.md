# React Router v7 Migration TODO

## Overview
Migrate from state-based navigation (ShoppingContext) to React Router v7 with file-based routing for better UX, SEO, and maintainability.

## Phase 1: Setup & Configuration

### 1. Install Dependencies
- [ ] Install React Router v7 packages:
  ```bash
  cd client
  bun add react-router @react-router/dev @react-router/fs-routes
  bun add -D @react-router/dev
  ```
- [ ] Update `vite.config.ts` to include React Router plugin
- [ ] Configure `app/routes.ts` for file-based routing

### 2. Project Structure Changes
- [ ] Create `app/` directory in client root
- [ ] Move current `src/App.tsx` content to `app/root.tsx`
- [ ] Create `app/routes/` directory for route modules
- [ ] Update `package.json` scripts for React Router commands

### 3. Configuration Files
- [ ] Update `vite.config.ts`:
  ```typescript
  import { reactRouter } from "@react-router/dev/vite";
  
  export default defineConfig({
    plugins: [reactRouter()],
  });
  ```
- [ ] Create `app/routes.ts` with flatRoutes configuration
- [ ] Update TypeScript config for new structure

## Phase 2: Route Structure Design

### 1. URL Structure Planning
Current state-based navigation → New URL structure:
- Dashboard (no bucket selected) → `/`
- Bucket view → `/bucket/:bucketId`
- Category view → `/bucket/:bucketId/category/:categoryId`
- Settings → `/settings`
- User profile → `/profile`

### 2. Route File Structure
```
app/routes/
├── _index.tsx                    # Dashboard home
├── bucket.$bucketId.tsx          # Bucket view (categories list)
├── bucket.$bucketId._index.tsx   # Bucket overview
├── bucket.$bucketId.category.$categoryId.tsx  # Category items view
├── settings.tsx                  # Settings page
└── profile.tsx                   # User profile
```

### 3. Layout Routes
- [ ] Create `app/routes/_layout.tsx` for shared sidebar layout
- [ ] Implement nested routing for bucket/category hierarchy
- [ ] Handle responsive layout in route layouts

## Phase 3: Data Loading Migration

### 1. Convert Context to Loaders
- [ ] Extract data fetching from `ShoppingContext` to route loaders
- [ ] Implement `clientLoader` functions for each route:
  ```typescript
  export async function clientLoader({ params }: Route.ClientLoaderArgs) {
    const { bucketId } = params;
    return getBucketData(bucketId);
  }
  ```
- [ ] Use `useLoaderData()` instead of context for route-specific data

### 2. Shared State Management
- [ ] Keep global state (user, theme) in context
- [ ] Move route-specific state to URL params and loaders
- [ ] Implement optimistic updates with `useNavigation()`

### 3. Data Persistence Integration
- [ ] Update localStorage integration to work with route changes
- [ ] Implement data synchronization between routes
- [ ] Handle data mutations with `clientAction` functions

## Phase 4: Component Migration

### 1. Navigation Components Update
- [ ] Update `BucketList` to use `<Link>` instead of state setters
- [ ] Modify `CategoryNavigation` for URL-based navigation
- [ ] Update breadcrumbs to reflect current route
- [ ] Replace state-based active indicators with URL matching

### 2. Main Content Components
- [ ] Migrate `Dashboard` component to `app/routes/_index.tsx`
- [ ] Create bucket route component in `app/routes/bucket.$bucketId.tsx`
- [ ] Create category route component for item management
- [ ] Update all navigation calls to use React Router navigation

### 3. Modal and Form Handling
- [ ] Update modals to work with route-based navigation
- [ ] Implement form submissions with `clientAction`
- [ ] Handle modal state with URL search params if needed
- [ ] Update CRUD operations to trigger route revalidation

## Phase 5: Mobile & Responsive Updates

### 1. Mobile Navigation
- [ ] Update mobile sidebar to use route-aware navigation
- [ ] Implement proper back button behavior
- [ ] Handle mobile drawer state with URL integration

### 2. Responsive Route Handling
- [ ] Implement mobile-specific route layouts
- [ ] Handle responsive navigation patterns
- [ ] Update mobile totals drawer for route changes

## Phase 6: Advanced Features

### 1. Search and Filtering
- [ ] Implement search with URL search params
- [ ] Add filtering capabilities via query parameters
- [ ] Use `useSearchParams()` for filter state management

### 2. Deep Linking & Sharing
- [ ] Enable direct links to specific buckets/categories
- [ ] Implement shareable URLs for shopping lists
- [ ] Add URL-based view mode persistence (card/simple view)

### 3. Browser Integration
- [ ] Implement proper page titles for each route
- [ ] Add meta tags for better SEO
- [ ] Handle browser back/forward navigation
- [ ] Implement keyboard shortcuts for navigation

## Phase 7: Testing & Validation

### 1. Navigation Testing
- [ ] Test all navigation flows work correctly
- [ ] Verify browser back/forward buttons
- [ ] Test deep linking to specific routes
- [ ] Validate mobile navigation behavior

### 2. Data Persistence Testing
- [ ] Ensure localStorage works across route changes
- [ ] Test data synchronization between routes
- [ ] Verify optimistic updates work correctly
- [ ] Test offline/online state handling

### 3. Performance Testing
- [ ] Verify code splitting works correctly
- [ ] Test route-based lazy loading
- [ ] Measure navigation performance
- [ ] Test with large datasets

## Migration Strategy

### Step-by-Step Approach
1. **Setup Phase**: Install and configure React Router v7
2. **Parallel Development**: Create new route structure alongside existing
3. **Component Migration**: Gradually move components to new structure
4. **Navigation Update**: Replace state-based navigation with URL-based
5. **Testing Phase**: Comprehensive testing of all flows
6. **Cleanup**: Remove old state-based navigation code

### Backwards Compatibility
- [ ] Maintain existing component APIs during transition
- [ ] Implement redirect logic for any existing bookmarks
- [ ] Provide fallback for unsupported routes
- [ ] Keep localStorage data structure compatible

### Risk Mitigation
- [ ] Create feature flag for new routing system
- [ ] Implement gradual rollout capability
- [ ] Maintain rollback plan to current system
- [ ] Document all breaking changes

## Expected Benefits

### User Experience
- ✅ Proper browser back/forward button support
- ✅ Bookmarkable URLs for specific buckets/categories
- ✅ Shareable links to shopping lists
- ✅ Better mobile navigation with proper history
- ✅ Faster navigation with code splitting

### Developer Experience
- ✅ Type-safe routing with automatic type generation
- ✅ File-based routing eliminates manual route configuration
- ✅ Better separation of concerns (route-specific vs global state)
- ✅ Easier testing with isolated route components
- ✅ Better debugging with clear URL structure

### Technical Benefits
- ✅ Automatic code splitting by route
- ✅ Better SEO with proper URLs and meta tags
- ✅ Reduced bundle size with lazy-loaded routes
- ✅ Improved caching strategies per route
- ✅ Better analytics tracking with route-based events

## Implementation Timeline

### Week 1: Setup & Planning
- Install dependencies and configure basic routing
- Create route file structure
- Set up development environment

### Week 2: Core Migration
- Migrate main dashboard and bucket routes
- Update navigation components
- Implement basic data loading

### Week 3: Advanced Features
- Add category routes and item management
- Implement mobile responsive routing
- Add search and filtering capabilities

### Week 4: Testing & Polish
- Comprehensive testing across all devices
- Performance optimization
- Documentation and cleanup

This migration will significantly improve the user experience while maintaining all existing functionality and providing a solid foundation for future features.